using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.Interfaces.Services;

namespace OrderFlowCore.Application.Services;

public class ValidationService : IValidationService
{
    private readonly HashSet<string> _protectedUsers = new() { "Admin" };

    public ServiceResult ValidateEmail(string email)
    {
        if (string.IsNullOrWhiteSpace(email))
            return ServiceResult.Success(); // Email is optional in most cases

        try
        {
            var emailValidator = new EmailAddressAttribute();
            if (!emailValidator.IsValid(email))
                return ServiceResult.Failure("البريد الإلكتروني غير صحيح");

            // Additional validation using MailAddress for more thorough checking
            var addr = new System.Net.Mail.MailAddress(email);
            if (addr.Address != email)
                return ServiceResult.Failure("البريد الإلكتروني غير صحيح");

            return ServiceResult.Success();
        }
        catch
        {
            return ServiceResult.Failure("البريد الإلكتروني غير صحيح");
        }
    }

    public ServiceResult ValidatePassword(string password, int minLength = 6)
    {
        if (string.IsNullOrWhiteSpace(password))
            return ServiceResult.Failure("كلمة المرور مطلوبة");

        if (password.Length < minLength)
            return ServiceResult.Failure($"كلمة المرور يجب أن تكون {minLength} حروف على الأقل");

        return ServiceResult.Success();
    }

    public ServiceResult ValidatePhone(string? phone)
    {
        if (string.IsNullOrWhiteSpace(phone))
            return ServiceResult.Success(); // Phone is optional

        // Clean phone number (remove non-digits)
        var cleanPhone = new string(phone.Where(char.IsDigit).ToArray());
        
        if (cleanPhone.Length < 10 || cleanPhone.Length > 15)
            return ServiceResult.Failure("رقم الهاتف غير صحيح");

        return ServiceResult.Success();
    }

    public ServiceResult ValidateUsername(string username)
    {
        if (string.IsNullOrWhiteSpace(username))
            return ServiceResult.Failure("اسم المستخدم مطلوب");

        if (username.Length < 3)
            return ServiceResult.Failure("اسم المستخدم يجب أن يكون 3 حروف على الأقل");

        if (username.Length > 50)
            return ServiceResult.Failure("اسم المستخدم يجب أن لا يتجاوز 50 حرف");

        // Check for valid characters (letters, numbers, underscore, hyphen)
        if (!Regex.IsMatch(username, @"^[a-zA-Z0-9\u0600-\u06FF\s._/-]+$"))
            return ServiceResult.Failure("اسم المستخدم يحتوي على أحرف غير مسموحة");

        return ServiceResult.Success();
    }

    public bool IsProtectedUser(string username)
    {
        return _protectedUsers.Contains(username);
    }

    public ServiceResult ValidatePasswordConfirmation(string password, string confirmPassword)
    {
        if (password != confirmPassword)
            return ServiceResult.Failure("كلمة المرور الجديدة وتأكيدها غير متطابقين");

        return ServiceResult.Success();
    }
}
