@model IEnumerable<OrderFlowCore.Application.DTOs.NationalityDto>
@{
    ViewData["Title"] = "إدارة الجنسيات";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="page-title">إدارة الجنسيات</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="@Url.Action("Index", "Dashboard")">لوحة التحكم</a></li>
                            <li class="breadcrumb-item active" aria-current="page">الجنسيات</li>
                        </ol>
                    </nav>
                </div>
                <a asp-action="Create" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة جنسية جديدة
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">قائمة الجنسيات</h5>
                    <div class="btn-group">
                        <form asp-action="ExportToExcel" method="post" style="display: inline;">
                            @Html.AntiForgeryToken()
                            <button type="submit" class="btn btn-outline-primary btn-sm me-2" title="تصدير إلى Excel">
                                <i class="fas fa-file-export me-1"></i>تصدير
                            </button>
                        </form>
                        <label class="btn btn-outline-success btn-sm mb-0"
                               data-bs-toggle="popover"
                               data-bs-placement="bottom"
                               data-bs-trigger="hover focus"
                               data-bs-html="true"
                               data-bs-title="<i class='fas fa-file-excel text-success me-2'></i>متطلبات ملف Excel"
                               data-bs-content="<div class='text-start'><small class='text-muted'>الأعمدة المطلوبة بالترتيب:</small><br><span class='badge bg-danger me-1'>1</span>اسم الجنسية <small class='text-danger'>(مطلوب)</small><br><span class='badge bg-secondary me-1'>2</span>الرمز <small class='text-muted'>(اختياري)</small><br><span class='badge bg-secondary me-1'>3</span>الوصف <small class='text-muted'>(اختياري)</small><br><span class='badge bg-secondary me-1'>4</span>نشط <small class='text-muted'>(نعم/لا - افتراضي: نعم)</small></div>">
                            <i class="fas fa-file-import me-1"></i>استيراد
                            <input type="file" id="importExcelFile" accept=".xlsx,.xls" hidden />
                        </label>
                    </div>
                </div>
                <div class="card-body">
                    @if (!Model.Any())
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-flag fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد جنسيات</h5>
                            <p class="text-muted">قم بإضافة جنسية جديدة للبدء</p>
                            <a asp-action="Create" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>إضافة جنسية جديدة
                            </a>
                        </div>
                    }
                    else
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>اسم الجنسية</th>
                                        <th>الرمز</th>
                                        <th>الوصف</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var nationality in Model)
                                    {
                                        <tr>
                                            <td>
                                                <strong>@nationality.Name</strong>
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(nationality.Code))
                                                {
                                                    <span class="badge bg-info">@nationality.Code</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(nationality.Description))
                                                {
                                                    <span class="text-muted">@nationality.Description</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">لا يوجد وصف</span>
                                                }
                                            </td>
                                            <td>
                                                @if (nationality.IsActive)
                                                {
                                                    <span class="badge bg-success">نشط</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-secondary">غير نشط</span>
                                                }
                                            </td>
                                            <td>
                                                <small class="text-muted">@nationality.CreatedAt.ToString("dd/MM/yyyy")</small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-action="Edit" asp-route-id="@nationality.Id" 
                                                       class="btn btn-sm btn-outline-primary" 
                                                       title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" 
                                                            class="btn btn-sm btn-outline-danger" 
                                                            onclick="confirmDelete(@nationality.Id, '@nationality.Name')"
                                                            title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الجنسية: <strong id="nationalityName"></strong>؟</p>
                <p class="text-danger"><small>لا يمكن التراجع عن هذا الإجراء.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Initialize popovers
        document.addEventListener('DOMContentLoaded', function() {
            var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl, {
                    container: 'body'
                });
            });
        });

        function confirmDelete(id, name) {
            document.getElementById('nationalityName').textContent = name;
            document.getElementById('deleteForm').action = '@Url.Action("Delete")/' + id;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        // Import functionality
        document.getElementById('importExcelFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (!file) return;

            if (confirm('هل أنت متأكد من استيراد بيانات الجنسيات من هذا الملف؟')) {
                const formData = new FormData();
                formData.append('excelFile', file);
                formData.append('__RequestVerificationToken', document.querySelector('input[name="__RequestVerificationToken"]').value);

                // Show loading
                const importBtn = e.target.parentElement;
                const originalText = importBtn.innerHTML;
                importBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الاستيراد...';
                importBtn.disabled = true;

                fetch('@Url.Action("ImportFromExcel")', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        let message = data.message;
                        if (data.errors && data.errors.length > 0) {
                            message += '\n\nالأخطاء:\n' + data.errors.join('\n');
                        }
                        alert(message);
                        location.reload();
                    } else {
                        alert('خطأ: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء استيراد الملف');
                })
                .finally(() => {
                    // Reset button
                    importBtn.innerHTML = originalText;
                    importBtn.disabled = false;
                    e.target.value = '';
                });
            } else {
                e.target.value = '';
            }
        });
    </script>
}