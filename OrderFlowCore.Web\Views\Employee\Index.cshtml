@model IEnumerable<OrderFlowCore.Application.DTOs.EmployeeDto>
@{
    ViewData["Title"] = "إدارة بيانات الموظفين";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="page-title">إدارة بيانات الموظفين</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="@Url.Action("Index", "Dashboard")">لوحة التحكم</a></li>
                            <li class="breadcrumb-item"><a href="@Url.Action("Index", "Settings")">الإعدادات</a></li>
                            <li class="breadcrumb-item active" aria-current="page">إدارة بيانات الموظفين</li>
                        </ol>
                    </nav>
                </div>
                <a asp-action="Create" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة موظف جديد
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-users me-2"></i>قائمة الموظفين
                    </h5>
                    <div class="btn-group">
                        <form asp-action="ExportToExcel" method="post" style="display: inline;">
                            @Html.AntiForgeryToken()
                            <button type="submit" class="btn btn-outline-light btn-sm me-2" title="تصدير إلى Excel">
                                <i class="fas fa-file-export me-1"></i>تصدير
                            </button>
                        </form>
                        <label class="btn btn-outline-light btn-sm mb-0"
                               data-bs-toggle="popover"
                               data-bs-placement="bottom"
                               data-bs-trigger="hover focus"
                               data-bs-html="true"
                               data-bs-title="<i class='fas fa-file-excel text-success me-2'></i>متطلبات ملف Excel"
                               data-bs-content="<div class='text-start'><small class='text-muted'>الأعمدة المطلوبة بالترتيب:</small><br><span class='badge bg-danger me-1'>1</span>الاسم <small class='text-danger'>(مطلوب)</small><br><span class='badge bg-secondary me-1'>2</span>الوظيفة <small class='text-muted'>(اختياري)</small><br><span class='badge bg-secondary me-1'>3</span>رقم الموظف <small class='text-muted'>(اختياري)</small><br><span class='badge bg-secondary me-1'>4</span>الرقم المدني <small class='text-muted'>(اختياري)</small><br><span class='badge bg-secondary me-1'>5</span>الجنسية <small class='text-muted'>(اختياري)</small><br><span class='badge bg-secondary me-1'>6</span>رقم الهاتف <small class='text-muted'>(اختياري)</small><br><span class='badge bg-secondary me-1'>7</span>نوع التوظيف <small class='text-muted'>(اختياري)</small><br><span class='badge bg-secondary me-1'>8</span>المؤهل <small class='text-muted'>(اختياري)</small></div>">
                            <i class="fas fa-file-import me-1"></i>استيراد
                            <input type="file" id="importExcelFile" accept=".xlsx,.xls" hidden />
                        </label>
                    </div>
                </div>
                <div class="card-body">
                    @if (Model != null && Model.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>#</th>
                                        <th>اسم الموظف</th>
                                        <th>الوظيفة</th>
                                        <th>السجل المدني</th>
                                        <th>الجنسية</th>
                                        <th>رقم الجوال</th>
                                        <th>نوع التوظيف</th>
                                        <th>المؤهل</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var employee in Model)
                                    {
                                        <tr>
                                            <td>@employee.Id</td>
                                            <td>@employee.Name</td>
                                            <td>@employee.Job</td>
                                            <td>@employee.CivilNumber</td>
                                            <td>@employee.Nationality</td>
                                            <td>@employee.Mobile</td>
                                            <td>@employee.EmploymentType</td>
                                            <td>@employee.Qualification</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-action="Edit" asp-route-id="@employee.Id" 
                                                       class="btn btn-sm btn-outline-primary" 
                                                       title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button"
                                                            class="btn btn-sm btn-outline-danger"
                                                            onclick="showDeleteModal(@employee.Id, '@employee.Name')"
                                                            title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد بيانات موظفين</h5>
                            <p class="text-muted">قم بإضافة موظف جديد للبدء</p>
                            <a asp-action="Create" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>إضافة موظف جديد
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>تأكيد الحذف
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <i class="fas fa-user-times fa-3x text-danger mb-3"></i>
                    <h6>هل أنت متأكد من حذف الموظف؟</h6>
                    <p class="text-muted mb-0">
                        <strong id="employeeName"></strong>
                    </p>
                    <small class="text-muted">لا يمكن التراجع عن هذا الإجراء</small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>إلغاء
                </button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i>حذف
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Initialize popovers
        document.addEventListener('DOMContentLoaded', function() {
            var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl, {
                    container: 'body'
                });
            });
        });

        function showDeleteModal(id, name) {
            document.getElementById('employeeName').textContent = name;
            document.getElementById('deleteForm').action = '@Url.Action("Delete")/' + id;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        // Import functionality
        document.getElementById('importExcelFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (!file) return;

            // Validate file type
            if (!file.name.toLowerCase().endsWith('.xlsx') && !file.name.toLowerCase().endsWith('.xls')) {
                alert('يرجى اختيار ملف Excel صالح (.xlsx أو .xls)');
                e.target.value = '';
                return;
            }

            // Validate file size (5MB)
            if (file.size > 5242880) {
                alert('حجم الملف يجب أن يكون أقل من 5 ميجابايت');
                e.target.value = '';
                return;
            }

            if (confirm('هل أنت متأكد من استيراد بيانات الموظفين من هذا الملف؟')) {
                const formData = new FormData();
                formData.append('excelFile', file);
                formData.append('__RequestVerificationToken', document.querySelector('input[name="__RequestVerificationToken"]').value);

                // Show loading
                const importBtn = e.target.parentElement;
                const originalText = importBtn.innerHTML;
                importBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الاستيراد...';
                importBtn.disabled = true;

                fetch('@Url.Action("ImportFromExcel")', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        let message = data.message;
                        if (data.errors && data.errors.length > 0) {
                            message += '\n\nالأخطاء:\n' + data.errors.join('\n');
                        }
                        alert(message);
                        location.reload(); // Refresh the page to show imported data
                    } else {
                        alert('خطأ: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء استيراد الملف');
                })
                .finally(() => {
                    // Reset button
                    importBtn.innerHTML = originalText;
                    importBtn.disabled = false;
                    e.target.value = '';
                });
            } else {
                e.target.value = '';
            }
        });
    </script>
}